{"name": "electrobox", "version": "1.0.1", "description": "AI Chat Desktop App with Electron and React", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:react\" \"wait-on http://localhost:5173 && npm run dev:electron\"", "dev:react": "cd renderer && npm run dev", "dev:electron": "NODE_ENV=development electron .", "build": "cd renderer && npm run build", "pack": "electron-builder --dir", "dist": "electron-builder", "install:renderer": "cd renderer && npm install"}, "keywords": ["electron", "react", "ai", "chat"], "author": "", "license": "ISC", "devDependencies": {"@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "concurrently": "^9.2.0", "electron": "^37.2.4", "electron-builder": "^26.0.12", "typescript": "^5.8.3", "wait-on": "^8.0.4"}, "dependencies": {"@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "electron-updater": "^6.6.2", "lucide-react": "^0.534.0"}, "build": {"appId": "com.electrobox.app", "mac": {"category": "public.app-category.productivity", "target": ["dmg", "zip"]}, "directories": {"buildResources": "build"}, "files": ["main.js", "preload.js", "database.js", "ai-service.js", "assets/**/*", "renderer/dist/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "renderer/dist", "to": "renderer/dist"}], "publish": [{"provider": "github", "owner": "jerome232", "repo": "electrobox"}]}}