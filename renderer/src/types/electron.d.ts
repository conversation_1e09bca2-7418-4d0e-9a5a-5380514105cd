export interface ElectronAPI {
  saveMessage: (message: any) => Promise<any>;
  getMessages: () => Promise<any>;
  clearMessages: () => Promise<any>;
  sendToAI: (data: string | { message: string; promptType?: string }) => Promise<any>;
  saveApiKey: (apiKey: string, name?: string, setActive?: boolean) => Promise<any>;
  getApiKeys: () => Promise<any>;
  getActiveApiKey: () => Promise<any>;
  setActiveApiKey: (id: number) => Promise<any>;
  deleteApiKey: (id: number) => Promise<any>;
  updateApiKeyName: (id: number, name: string) => Promise<any>;
  getApiKey: () => Promise<any>;
  testApiKey: (apiKey: string) => Promise<any>;
  
  // System prompt settings
  getSystemPromptSettings: () => Promise<any>;
  getDefaultSystemPrompt: () => Promise<any>;
  setDefaultSystemPrompt: (id: number) => Promise<any>;
  saveSystemPromptSetting: (name: string, description: string, prompts: any, isDefault?: boolean) => Promise<any>;
  updateSystemPromptSetting: (id: number, name: string, description: string, prompts: any) => Promise<any>;
  deleteSystemPromptSetting: (id: number) => Promise<any>;
  
  getAppVersion: () => Promise<string>;

  // Character settings
  saveCharacterSetting: (characterSetting: any) => Promise<any>;
  getCharacterSettings: () => Promise<any>;
  getCharacterSetting: (id: number) => Promise<any>;

  // World settings
  saveWorldSetting: (worldSetting: any) => Promise<any>;
  getWorldSettings: () => Promise<any>;
  getWorldSetting: (id: number) => Promise<any>;

  // Chats
  saveChat: (parentId: number | null, settingId: number, settingType: string, contents: string) => Promise<any>;
  getChats: (settingId: number, settingType: string) => Promise<any>;
  getRecentChats: (limit?: number) => Promise<any>;

  // Persona creation
  savePersona: (persona: any) => Promise<any>;
  getPersonas: () => Promise<any>;
  getPersona: (id: string) => Promise<any>;
  deletePersona: (id: string) => Promise<any>;
  enhanceCharacterText: (data: { text: string; field: 'personality' | 'appearance'; characterName: string; imageBase64?: string }) => Promise<any>;
  enhanceCharacterTextStream: (data: { text: string; field: 'personality' | 'appearance'; characterName: string; imageBase64?: string }) => Promise<any>;
  onEnhanceCharacterTextChunk: (callback: (event: any, data: { chunk: string; field: string }) => void) => void;
  removeAllListeners: (channel: string) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
} 