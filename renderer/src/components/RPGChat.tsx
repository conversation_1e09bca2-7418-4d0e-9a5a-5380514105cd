import React, { useState, useRef, useEffect, useLayoutEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { ChatArea } from './chat/ChatArea';
import { CharacterSheet } from './character/CharacterSheet';
import { ChoiceButtons } from './chat/ChoiceButtons';
import { SceneBackground } from './scene/SceneBackground';
import { SaveLoadPanel } from './save/SaveLoadPanel';
import { Button } from './ui/button';
import { Save, Menu, Settings, ArrowLeft, Sparkles } from 'lucide-react';
import { ChatParameters } from './ChatSetup';
import { PersonaImage } from './ui/persona-image';

export interface Character {
  id: string;
  name: string;
  avatar: string;
  stats: {
    strength: number;
    charm: number;
    arcana: number;
    luck: number;
  };
  inventory: InventoryItem[];
  relationships: Relationship[];
  level: number;
  experience: number;
}

export interface InventoryItem {
  id: string;
  name: string;
  type: 'weapon' | 'armor' | 'potion' | 'artifact' | 'misc';
  rarity: 'common' | 'uncommon' | 'rare' | 'legendary';
  description: string;
  effects?: string[];
}

export interface Relationship {
  characterName: string;
  level: number; // -100 to 100
  status: string; // "Hostile", "Neutral", "Friendly", "Allied"
}

export interface ChatMessage {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  timestamp: number;
  characterName?: string;
  emotion?: 'neutral' | 'happy' | 'angry' | 'sad' | 'mysterious';
  choices?: DialogueChoice[];
}

export interface DialogueChoice {
  id: string;
  text: string;
  preview?: string;
  requirements?: {
    stat?: keyof Character['stats'];
    minValue?: number;
    hasItem?: string;
  };
  consequence?: string;
}

export interface GameState {
  currentScene: string;
  sceneBackground: string;
  mood: 'light' | 'neutral' | 'dark' | 'mysterious' | 'combat';
  chapter: string;
  questLog: string[];
}

// Timeline node type
interface TimelineNode {
  id: string;
  message: string;
  sender: 'user' | 'ai';
  choices?: DialogueChoice[];
  userInput?: string;
  parent?: TimelineNode | null;
  children: TimelineNode[];
  choiceId?: string; // The id of the choice that led to this node
  ref?: React.RefObject<HTMLDivElement>; // Added for measuring position
}

// Helper function to get character emoji based on class
const getCharacterEmoji = (characterClass: string): string => {
  const emojiMap: { [key: string]: string } = {
    adventurer: '🗡️',
    wizard: '🧙‍♂️',
    warrior: '⚔️',
    rogue: '🥷',
    cleric: '⛪',
    ranger: '🏹',
    bard: '🎵',
    monk: '🧘'
  };
  return emojiMap[characterClass] || '🗡️';
};

// Helper function to get scene background based on world setting
const getSceneBackground = (worldSetting: string): string => {
  const backgroundMap: { [key: string]: string } = {
    fantasy: 'forest',
    'sci-fi': 'space',
    'post-apocalyptic': 'wasteland',
    medieval: 'castle',
    cyberpunk: 'city',
    steampunk: 'factory',
    western: 'desert',
    modern: 'urban'
  };
  return backgroundMap[worldSetting] || 'forest';
};

// Helper function to create initial timeline based on parameters
const createInitialTimeline = (parameters?: ChatParameters): TimelineNode => {
  console.log('🎬 createInitialTimeline called with parameters:', parameters);
  
  if (!parameters) {
    console.log('🔄 No parameters provided, using default timeline');
    return {
      id: 'root',
      message: "Welcome to your adventure! What would you like to do?",
      sender: 'ai',
      choices: [
        { id: 'start', text: 'Begin my journey', preview: 'start the adventure' }
      ],
      children: [],
      parent: null
    };
  }

  let initialMessage = "Welcome to your adventure! What would you like to do?";
  let choices = [
    { id: 'start', text: 'Begin my journey', preview: 'start the adventure' }
  ];

  if (parameters.settingType === 'character' && parameters.characterSetting) {
    console.log('👤 Creating timeline for character setting:', parameters.characterSetting);
    // Use the actual initial message from character setting
    initialMessage = parameters.characterSetting.initialMessage || initialMessage;
    console.log('💬 Using initial message:', initialMessage);
    // Generate contextual choices based on character setting
    choices = generateCharacterChoices(parameters.characterSetting);
    console.log('🎯 Generated character choices:', choices);
  } else if (parameters.settingType === 'world' && parameters.worldSetting) {
    console.log('🌍 Creating timeline for world setting:', parameters.worldSetting);
    // Use generated world opening message
    initialMessage = `Welcome to ${parameters.worldSetting.name}. ${parameters.worldSetting.worldDescription}`;
    console.log('💬 Using world initial message:', initialMessage);
    // Generate contextual choices based on world setting
    choices = generateWorldChoices(parameters.worldSetting);
    console.log('🎯 Generated world choices:', choices);
  }

  const timeline: TimelineNode = {
    id: 'root',
    message: initialMessage,
    sender: 'ai' as const,
    choices: choices,
    children: [],
    parent: null
  };
  
  console.log('✅ Created initial timeline:', timeline);
  return timeline;
};

// Helper: generate contextual choices for character-based chats
const generateCharacterChoices = (characterSetting: any) => {
  // These should be AI-generated based on the character's personality and scenario
  // For now, using contextual defaults that will be replaced by AI generation
  return [
    { id: 'personality', text: 'Act according to your nature', preview: 'respond based on your personality' },
    { id: 'scenario', text: 'Engage with the scenario', preview: 'interact with your current situation' },
    { id: 'explore', text: 'Explore your surroundings', preview: 'investigate the environment' },
    { id: 'reflect', text: 'Reflect on your situation', preview: 'think about your circumstances' }
  ];
};

// Helper: generate contextual choices for world-based chats
const generateWorldChoices = (worldSetting: any) => {
  // These should be AI-generated based on the world description and character role
  // For now, using contextual defaults that will be replaced by AI generation
  return [
    { id: 'role', text: 'Embrace your role', preview: 'act according to your position in this world' },
    { id: 'world', text: 'Explore the world', preview: 'discover more about your environment' },
    { id: 'interact', text: 'Seek interaction', preview: 'look for others to engage with' },
    { id: 'adapt', text: 'Adapt to the situation', preview: 'adjust to your circumstances' }
  ];
};

// Helper: collect all nodes in the tree for navigation
function collectTimelineNodes(root: TimelineNode): TimelineNode[] {
  const nodes: TimelineNode[] = [];
  function traverse(node: TimelineNode) {
    nodes.push(node);
    node.children.forEach(traverse);
  }
  traverse(root);
  return nodes;
}

// Helper: get path from root to a node
function getTimelinePathToNode(node: TimelineNode): TimelineNode[] {
  const path: TimelineNode[] = [];
  let n: TimelineNode | undefined = node;
  while (n) {
    path.unshift(n);
    n = n.parent!;
  }
  return path;
}

// Helper: recursively collect node positions for line drawing (relative to container)
function collectNodePositions(node: TimelineNode, positions: { [key: string]: { x: number; y: number; width: number; height: number; parentId: string | null } }, parentId: string | null = null, containerRect: { left: number; top: number } = { left: 0, top: 0 }) {
  if (!node || !node.ref || !node.ref.current) return;
  const rect = node.ref.current.getBoundingClientRect();
  positions[node.id] = {
    x: rect.left - containerRect.left + rect.width / 2,
    y: rect.top - containerRect.top + rect.height / 2,
    width: rect.width,
    height: rect.height,
    parentId,
  };
  node.children.forEach(child => collectNodePositions(child, positions, node.id, containerRect));
}

// Helper: render the timeline as a horizontal mind map with dynamic lines
function FloatingTimelineTree({ node, currentNode, onJump, depth = 0, nodeRefs }: { node: TimelineNode, currentNode: TimelineNode, onJump: (n: TimelineNode) => void, depth?: number, nodeRefs: { [key: string]: React.RefObject<HTMLDivElement> } }) {
  const thisRef = React.useRef<HTMLDivElement>(null);
  node.ref = thisRef as React.RefObject<HTMLDivElement>;
  if (nodeRefs) nodeRefs[node.id] = thisRef as React.RefObject<HTMLDivElement>;
  const hasChildren = node.children.length > 0;
  
  // Strip markdown for timeline preview
  const stripMarkdown = (text: string) => {
    return text
      .replace(/#{1,6}\s+/g, '') // Remove headers
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
      .replace(/\*(.*?)\*/g, '$1') // Remove italic
      .replace(/`(.*?)`/g, '$1') // Remove inline code
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Remove links
      .replace(/\n/g, ' ') // Replace newlines with spaces
      .trim();
  };
  
  const previewText = stripMarkdown(node.message).slice(0, 24) + (stripMarkdown(node.message).length > 24 ? '...' : '');
  
  return (
    <div style={{ display: 'flex', alignItems: 'center', position: 'relative', minHeight: 60 }}>
      <div ref={thisRef} style={{ zIndex: 2 }}>
        {node.sender === 'user' ? (
          <span className="text-xs px-2 py-1 rounded font-mono italic text-muted-foreground bg-muted/40 cursor-default select-none opacity-70 shadow max-w-[200px] break-words">
            You: {previewText}
          </span>
        ) : (
          <button
            className={`text-xs px-2 py-1 rounded border shadow max-w-[200px] break-words ${node === currentNode ? 'bg-accent text-accent-foreground font-bold border-accent' : 'bg-muted/30 text-muted-foreground border-border hover:bg-accent/20'} transition`}
            onClick={() => onJump(node)}
            disabled={node === currentNode}
            style={{ minWidth: 120, maxWidth: 200 }}
            title={stripMarkdown(node.message)} // Show full text on hover
          >
            {previewText}
          </button>
        )}
      </div>
      {hasChildren && (
        <div style={{ display: 'flex', flexDirection: 'column', marginLeft: 60, position: 'relative' }}>
          {node.children.map((child, i) => (
            <div key={child.id} style={{ marginTop: i === 0 ? 0 : 40 }}>
              <FloatingTimelineTree node={child} currentNode={currentNode} onJump={onJump} depth={depth + 1} nodeRefs={nodeRefs} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface RPGChatProps {
  onBackToDirectory?: () => void;
  parameters?: ChatParameters;
}

const RPGChat: React.FC<RPGChatProps> = ({ onBackToDirectory, parameters }) => {
  // Initialize character based on parameters
  const [character, setCharacter] = useState<Character>(() => {
    if (!parameters) {
      return {
        id: '1',
        name: 'Adventurer',
        avatar: '🗡️',
        stats: {
          strength: 10,
          charm: 10,
          arcana: 10,
          luck: 10
        },
        inventory: [],
        relationships: [],
        level: 1,
        experience: 0
      };
    }

    const baseStats = 10;
    // No difficulty in ChatParameters, so default to 3
    const difficulty = 3;
    const statBonus = Math.max(0, difficulty - 3) * 2;

    let characterName = 'Adventurer';
    let characterClass = 'adventurer';
    if (parameters.settingType === 'character' && parameters.characterSetting) {
      characterName = parameters.characterSetting.name || characterName;
      characterClass = parameters.characterSetting.character || characterClass;
    } else if (parameters.settingType === 'world' && parameters.worldSetting) {
      characterName = parameters.worldSetting.characterRole || characterName;
      characterClass = parameters.worldSetting.characterRole || characterClass;
    }

    return {
      id: Date.now().toString(),
      name: characterName,
      avatar: getCharacterEmoji(characterClass),
      stats: {
        strength: baseStats + (characterClass === 'warrior' ? 3 : 0) + statBonus,
        charm: baseStats + (characterClass === 'bard' ? 3 : 0) + statBonus,
        arcana: baseStats + (characterClass === 'wizard' ? 3 : 0) + statBonus,
        luck: baseStats + statBonus
      },
      inventory: [],
      relationships: [],
      level: 1,
      experience: 0
    };
  });

  // Initialize empty messages array
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  // Initialize game state based on parameters
  const [gameState, setGameState] = useState<GameState>(() => {
    if (!parameters) {
      return {
        currentScene: 'Unknown Location',
        sceneBackground: 'forest',
        mood: 'neutral',
        chapter: 'Chapter 1: The Beginning',
        questLog: ['Explore the world']
      };
    }

    let currentScene = 'Unknown Location';
    let sceneBackground = 'forest';
    let mood: GameState['mood'] = 'neutral';
    let questLog: string[] = ['Explore the world'];

    if (parameters.settingType === 'character' && parameters.characterSetting) {
      currentScene = parameters.characterSetting.scenario || currentScene;
      // No worldSetting, so fallback to 'forest'
      sceneBackground = 'forest';
      // No genre, so fallback to 'neutral'
      mood = 'neutral';
      // No quest, so fallback
      questLog = ['Explore the world'];
    } else if (parameters.settingType === 'world' && parameters.worldSetting) {
      currentScene = parameters.worldSetting.name || currentScene;
      sceneBackground = getSceneBackground(parameters.worldSetting.name || 'forest');
      mood = 'neutral';
      questLog = ['Explore the world'];
    }

    return {
      currentScene,
      sceneBackground,
      mood,
      chapter: 'Chapter 1: The Beginning',
      questLog
    };
  });

  const [timelineRoot, setTimelineRoot] = useState<TimelineNode>(() => {
    console.log('🎯 RPGChat: Initializing timeline with parameters:', parameters);
    
    // Check if we have a full timeline first (from reconstructed chat history)
    if (parameters && (parameters as any).fullTimeline) {
      const fullTimeline = (parameters as any).fullTimeline;
      console.log('🔄 RPGChat: Found full timeline from chat history:', fullTimeline);
      return fullTimeline;
    }
    
    // Check if we have existing chat data (fallback for single chat entry)
    if (parameters && (parameters as any).existingChat) {
      const existingChat = (parameters as any).existingChat;
      console.log('📄 RPGChat: Found existing chat data:', existingChat);
      
      // Create timeline from existing chat data
      const existingTimeline: TimelineNode = {
        id: 'existing-chat',
        message: existingChat.content || 'Welcome back to your adventure!',
        sender: 'ai',
        choices: existingChat.options?.map((option: string, index: number) => ({
          id: `existing-choice-${index}`,
          text: option,
          preview: option.toLowerCase()
        })) || [],
        children: [],
        parent: null
      };
      
      console.log('✅ RPGChat: Created existing timeline:', existingTimeline);
      return existingTimeline;
    }
    
    console.log('🆕 RPGChat: No existing chat, creating initial timeline');
    // Otherwise create initial timeline
    return createInitialTimeline(parameters);
  });
  
  const [currentNode, setCurrentNode] = useState<TimelineNode>(timelineRoot);
  const [isTyping, setIsTyping] = useState(false);
  const [showSavePanel, setShowSavePanel] = useState(false);
  const [showCharacterSheet, setShowCharacterSheet] = useState(true);
  const chatAreaRef = useRef<HTMLDivElement>(null);
  // Collapsible timeline state
  const [timelineOpen, setTimelineOpen] = useState(true);
  const [_, forceUpdate] = useState(0);

  // New state for enhanced chat features
  const [messageInput, setMessageInput] = useState('');
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [isGeneratingOptions, setIsGeneratingOptions] = useState(false);
  const [additionalOptions, setAdditionalOptions] = useState<DialogueChoice[]>([]);
  const [isStreamingResponse, setIsStreamingResponse] = useState(false);
  const [streamingContent, setStreamingContent] = useState(''); // for rerendering after tree mutation
  const [svgLines, setSvgLines] = useState<{ x1: number; y1: number; x2: number; y2: number }[]>([]);
  const [apiKeyError, setApiKeyError] = useState<string | null>(null);
  const timelineContainerRef = useRef<HTMLDivElement>(null);
  const nodeRefs: { [key: string]: React.RefObject<HTMLDivElement> } = {};

  // All nodes in the tree (for navigation)
  const allTimelineNodes = collectTimelineNodes(timelineRoot);

  // Handle choice selection (branching) - updated to use streaming
  const handleChoiceSelect = async (choice: DialogueChoice) => {
    setIsTyping(true);

    // Check if a user node for this choice already exists as a child of currentNode
    let userNode = currentNode.children.find(
      (child) => child.sender === 'user' && child.message === choice.text
    );

    if (!userNode) {
      // Create user node
      userNode = {
        id: Date.now().toString() + '-user',
        message: choice.text,
        sender: 'user',
        parent: currentNode,
        children: [],
        choiceId: choice.id
      };
      currentNode.children.push(userNode);

      try {
        // Generate AI response with streaming
        const aiResponse = await generateAIResponseWithStreaming(choice.text);

        // Generate choices for the response
        const choices = await generateChoicesForResponse(aiResponse, choice.text);

        // Create AI response node with new options
        const newChoices = choices.map((option: string, index: number) => ({
          id: `choice-${Date.now()}-${index}`,
          text: option,
          preview: option.toLowerCase()
        }));

        const aiNode: TimelineNode = {
          id: Date.now().toString() + '-ai',
          message: aiResponse,
          sender: 'ai',
          choices: newChoices,
          parent: userNode,
          children: []
        };
        userNode.children.push(aiNode);

        // Save the new interaction to database
        if (parameters) {
          const settingId = parameters.settingType === 'character'
            ? parameters.characterSetting?.id
            : parameters.worldSetting?.id;

          if (settingId) {
            const chatContent = {
              content: aiResponse,
              options: choices,
              userChoice: choice.text // Store which choice the user made
            };

            try {
              await window.electronAPI.saveChat(
                null, // parent ID - could be enhanced for branching
                settingId,
                parameters.settingType,
                JSON.stringify(chatContent)
              );
            } catch (error) {
              console.error('Failed to save chat interaction:', error);
            }
          }
        }

        setCurrentNode(aiNode);
        setAdditionalOptions([]); // Reset additional options

      } catch (error) {
        console.error('Failed to generate AI response:', error);
        // Remove the user node if AI response failed
        currentNode.children.pop();
      }
    } else {
      // If already exists, jump to the existing branch
      setCurrentNode(userNode.children[0]);
    }

    setIsTyping(false);
    forceUpdate((n) => n + 1); // force rerender to update tree
  };

  // Generate AI response with streaming and structured JSON
  const generateAIResponseWithStreaming = async (userMessage: string): Promise<string> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    const context = buildChatContext();
    const prompt = `Based on this context:
${context}

The user said: "${userMessage}"

Respond naturally and engagingly to the user's message. Focus on providing a detailed, contextually appropriate response that advances the conversation. 

You can use markdown formatting to enhance your response:
- Use **bold** for emphasis on important words or actions
- Use *italic* for thoughts, emotions, or subtle details
- Use \`code\` for special terms, names, or technical references
- Use > blockquotes for dramatic dialogue or important statements
- Use lists for describing multiple items or actions

Do not include any choice options or JSON formatting - just respond as the character or world would naturally respond with rich, immersive storytelling.`;

    // Use the AI service with the ai_character prompt type
    try {
      const response = await window.electronAPI.sendToAI({
        message: prompt,
        promptType: 'ai_character'
      });
      
      if (response.success) {
        return response.response;
      } else {
        throw new Error(response.error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('AI service error:', error);
      // Fallback to direct API call
      return await generateAIResponseDirect(userMessage, prompt);
    }
  };

  const generateAIResponseDirect = async (userMessage: string, prompt: string): Promise<string> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    // Add markdown instructions to the prompt if not already present
    const enhancedPrompt = prompt.includes('markdown formatting') ? prompt : `${prompt}

You can use markdown formatting to enhance your response:
- Use **bold** for emphasis on important words or actions
- Use *italic* for thoughts, emotions, or subtle details
- Use \`code\` for special terms, names, or technical references
- Use > blockquotes for dramatic dialogue or important statements
- Use lists for describing multiple items or actions`;

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Electrobox Chat'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-chat-v3-0324',
        messages: [{ role: 'user', content: enhancedPrompt }],
        max_tokens: 500,
        temperature: 0.7,
        stream: true
      })
    });

    if (!response.ok || !response.body) {
      throw new Error('Failed to get AI response');
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    let buffer = '';

    setIsStreamingResponse(true);
    setStreamingContent('');

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const dataStr = line.slice(6).trim();
            if (dataStr === '[DONE]') continue;

            try {
              const data = JSON.parse(dataStr);
              const delta = data.choices?.[0]?.delta?.content || '';
              if (delta) {
                fullContent += delta;
                setStreamingContent(fullContent);
              }
            } catch (e) {
              // Ignore parsing errors for individual chunks
            }
          }
        }
      }

      return fullContent || "I continue the conversation...";

    } finally {
      setIsStreamingResponse(false);
      setStreamingContent('');
    }
  };

  const generateChoicesForResponse = async (aiResponse: string, userMessage: string): Promise<string[]> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    const context = buildChatContext();
    const prompt = `Based on this context:
${context}

The user said: "${userMessage}"
The AI responded: "${aiResponse}"

Generate 4 different choice options that the user could select to continue the conversation. Each option should be 2-6 words and represent different ways the user could respond.

You MUST respond with ONLY valid JSON in this exact format - no other text, no explanations:
{
  "options": [
    "option 1 text (2-6 words)",
    "option 2 text (2-6 words)", 
    "option 3 text (2-6 words)",
    "option 4 text (2-6 words)"
  ]
}

CRITICAL: Start your response with { and end with }. Do not include any text before or after the JSON. The options should be varied and interesting ways the user could respond next.`;

    // Use the AI service with the choice_generation prompt type
    try {
      const response = await window.electronAPI.sendToAI({
        message: prompt,
        promptType: 'choice_generation'
      });
      
      if (response.success) {
        // Parse the JSON response
        try {
          // Extract JSON from potential markdown code blocks
          let jsonContent = response.response.trim();
          
          // Remove markdown code block markers if present
          if (jsonContent.startsWith('```json')) {
            jsonContent = jsonContent.replace(/^```json\s*/, '');
          }
          if (jsonContent.startsWith('```')) {
            jsonContent = jsonContent.replace(/^```\s*/, '');
          }
          if (jsonContent.endsWith('```')) {
            jsonContent = jsonContent.replace(/\s*```$/, '');
          }
          
          // Find the first { and last } to extract JSON
          const firstBrace = jsonContent.indexOf('{');
          const lastBrace = jsonContent.lastIndexOf('}');
          
          if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
            jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
          }
          
          const jsonResponse = JSON.parse(jsonContent);
          if (jsonResponse.options && Array.isArray(jsonResponse.options)) {
            return jsonResponse.options.slice(0, 4);
          }
        } catch (e) {
          console.error('Failed to parse choices JSON response:', e);
        }
      } else {
        throw new Error(response.error || 'Failed to get choices response');
      }
    } catch (error) {
      console.error('AI service error for choices:', error);
      // Fallback to direct API call
      return await generateChoicesDirect(aiResponse, userMessage, prompt);
    }

    // Fallback
    return ["Continue", "Ask question", "Take action", "Observe"];
  };

  const generateChoicesDirect = async (aiResponse: string, userMessage: string, prompt: string): Promise<string[]> => {
    const apiKey = await getOpenRouterApiKey();
    if (!apiKey) {
      throw new Error('No API key found');
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Electrobox Chat'
      },
      body: JSON.stringify({
        model: 'deepseek/deepseek-chat-v3-0324',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 300,
        temperature: 0.7,
        response_format: { type: 'json_object' }
      })
    });

    if (!response.ok) {
      throw new Error('Failed to get choices response');
    }

    const responseData = await response.json();
    const content = responseData.choices?.[0]?.message?.content || '';

    try {
      // Try to extract JSON from the response if it contains extra text
      let jsonContent = content.trim();
      
      // Find the first { and last } to extract JSON
      const firstBrace = jsonContent.indexOf('{');
      const lastBrace = jsonContent.lastIndexOf('}');
      
      if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
        jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
      }
      
      const jsonResponse = JSON.parse(jsonContent);
      if (jsonResponse.options && Array.isArray(jsonResponse.options)) {
        return jsonResponse.options.slice(0, 4);
      }
    } catch (e) {
      console.error('Failed to parse choices JSON response:', e);
      console.log('Raw choices response:', content);
    }

    // Fallback if JSON parsing fails
    return ["Continue", "Ask question", "Take action", "Observe"];
  };

  const generateAIResponse = async (choice: DialogueChoice, parameters?: ChatParameters): Promise<string> => {
    // Legacy function - now calls the streaming version
    try {
      const result = await generateAIResponseWithStreaming(choice.text);
      return result;
    } catch (error) {
      console.error('AI response generation failed:', error);
      return "Your choice leads you forward in your journey...";
    }
  };

  const generateNewChoices = async (previousChoiceId: string, parameters?: ChatParameters): Promise<DialogueChoice[]> => {
    // Generate contextual choices based on the previous choice and world parameters
    const choiceMap: { [key: string]: DialogueChoice[] } = {
      explore: [
        { id: 'investigate', text: 'Investigate further', preview: 'look deeper into the area' },
        { id: 'move', text: 'Move to a new area', preview: 'explore elsewhere' },
        { id: 'rest', text: 'Take a moment to rest', preview: 'recover and plan' }
      ],
      quest: [
        { id: 'advance', text: 'Advance toward your goal', preview: 'make progress on your quest' },
        { id: 'gather', text: 'Gather information', preview: 'learn more about your objective' },
        { id: 'prepare', text: 'Prepare for challenges', preview: 'get ready for what lies ahead' }
      ],
      interact: [
        { id: 'approach', text: 'Approach any people you see', preview: 'make contact with NPCs' },
        { id: 'observe', text: 'Observe from a distance', preview: 'watch and learn' },
        { id: 'signal', text: 'Signal for help', preview: 'try to attract attention' }
      ],
      prepare: [
        { id: 'equipment', text: 'Check your equipment', preview: 'review your gear' },
        { id: 'abilities', text: 'Review your abilities', preview: 'consider your skills' },
        { id: 'plan', text: 'Make a plan', preview: 'think about your next steps' }
      ],
      start: [
        { id: 'explore', text: 'Explore the area', preview: 'investigate your surroundings' },
        { id: 'quest', text: 'Begin your quest', preview: 'start your main objective' },
        { id: 'interact', text: 'Look for others', preview: 'seek out NPCs or companions' }
      ]
    };

    return choiceMap[previousChoiceId] || [
      { id: 'continue', text: 'Continue your journey', preview: 'move forward' }
    ];
  };

  // Get OpenRouter API key
  const getOpenRouterApiKey = async (): Promise<string | null> => {
    try {
      const result = await window.electronAPI.getActiveApiKey();
      if (result.success && result.apiKey) {
        return result.apiKey.api_key;
      }
      console.error('No API key found in database');
      return null;
    } catch (error) {
      console.error('Failed to get API key:', error);
      return null;
    }
  };

  // Build chat context for AI prompts
  const buildChatContext = (): string => {
    let context = '';

    if (parameters?.settingType === 'character' && parameters.characterSetting) {
      const char = parameters.characterSetting;
      context = `Character: ${char.name}
Personality: ${char.personality || 'Not specified'}
Scenario: ${char.scenario || 'Not specified'}
Current situation: ${currentNode.message}`;
    } else if (parameters?.settingType === 'world' && parameters.worldSetting) {
      const world = parameters.worldSetting;
      context = `World: ${world.name}
World Description: ${world.worldDescription || 'Not specified'}
Character Role: ${world.characterRole || 'Not specified'}
Current situation: ${currentNode.message}`;
    } else {
      context = `Current situation: ${currentNode.message}`;
    }

    return context;
  };

  // Enhanced prompt functionality
  const handleEnhancePrompt = async () => {
    if (!messageInput.trim()) return;

    setIsEnhancing(true);
    try {
      const apiKey = await getOpenRouterApiKey();
      if (!apiKey) {
        console.error('No API key found');
        return;
      }

      const context = buildChatContext();
      const prompt = `Based on this chat context:
${context}

The user has written: "${messageInput}"

Enhance and improve this message to be more engaging, detailed, and contextually appropriate. Keep the user's intent but make it more immersive and well-written. Return only the enhanced message, no explanations.`;

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Electrobox Chat'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 200,
          temperature: 0.7
        })
      });

      if (response.ok) {
        const data = await response.json();
        const enhancedText = data.choices?.[0]?.message?.content?.trim();
        if (enhancedText) {
          setMessageInput(enhancedText);
        }
      }
    } catch (error) {
      console.error('Failed to enhance prompt:', error);
    } finally {
      setIsEnhancing(false);
    }
  };

  // Generate more options functionality
  const handleGenerateMoreOptions = async () => {
    setIsGeneratingOptions(true);
    try {
      const apiKey = await getOpenRouterApiKey();
      if (!apiKey) {
        console.error('No API key found');
        return;
      }

      const context = buildChatContext();
      const existingOptions = [...(currentNode.choices || []), ...additionalOptions];
      const existingTexts = existingOptions.map(opt => opt.text).join(', ');

      const prompt = `Based on this chat context:
${context}

Existing options: ${existingTexts}

Generate exactly 4 NEW contextual response options that are different from the existing ones. Each option should be 2-6 words and offer unique ways to respond or act.

Respond with a JSON object in this format:
{
  "options": [
    "option 1 text",
    "option 2 text", 
    "option 3 text",
    "option 4 text"
  ]
}`;

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'Electrobox Chat'
        },
        body: JSON.stringify({
          model: 'deepseek/deepseek-chat-v3-0324',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 150,
          temperature: 0.8,
          response_format: { type: 'json_object' }
        })
      });

      if (response.ok) {
        const data = await response.json();
        const generatedContent = data.choices?.[0]?.message?.content?.trim();

        if (generatedContent) {
          try {
            // Extract JSON from potential markdown code blocks
            let jsonContent = generatedContent.trim();
            
            // Remove markdown code block markers if present
            if (jsonContent.startsWith('```json')) {
              jsonContent = jsonContent.replace(/^```json\s*/, '');
            }
            if (jsonContent.startsWith('```')) {
              jsonContent = jsonContent.replace(/^```\s*/, '');
            }
            if (jsonContent.endsWith('```')) {
              jsonContent = jsonContent.replace(/\s*```$/, '');
            }
            
            // Find the first { and last } to extract JSON
            const firstBrace = jsonContent.indexOf('{');
            const lastBrace = jsonContent.lastIndexOf('}');
            
            if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
              jsonContent = jsonContent.substring(firstBrace, lastBrace + 1);
            }
            
            // Parse the JSON response
            const responseData = JSON.parse(jsonContent);
            
            if (responseData.options && Array.isArray(responseData.options)) {
              const newOptions = responseData.options
                .slice(0, 4)
                .map((text: string, index: number) => ({
                  id: `generated-${Date.now()}-${index}`,
                  text: String(text),
                  preview: String(text).toLowerCase()
                }));

              if (newOptions.length > 0) {
                setAdditionalOptions(prev => [...prev, ...newOptions]);
                
                // Save the combined options (original + additional) to database
                if (parameters) {
                  const settingId = parameters.settingType === 'character'
                    ? parameters.characterSetting?.id
                    : parameters.worldSetting?.id;

                  if (settingId) {
                    const allOptions = [
                      ...(currentNode.choices || []).map(choice => choice.text),
                      ...additionalOptions.map(choice => choice.text),
                      ...newOptions.map(choice => choice.text)
                    ];

                    const chatContent = {
                      content: currentNode.message,
                      options: allOptions
                    };

                    try {
                      await window.electronAPI.saveChat(
                        null, // parent ID - could be enhanced for branching
                        settingId,
                        parameters.settingType,
                        JSON.stringify(chatContent)
                      );
                      console.log('✅ Updated chat options saved to database');
                    } catch (error) {
                      console.error('Failed to save updated chat options:', error);
                    }
                  }
                }
              }
            }
          } catch (e) {
            console.error('Failed to parse JSON options:', e);
            console.log('Raw response:', generatedContent);
          }
        }
      }
    } catch (error) {
      console.error('Failed to generate more options:', error);
    } finally {
      setIsGeneratingOptions(false);
    }
  };

  // Handle custom user message submission
  const handleSubmitMessage = async () => {
    if (!messageInput.trim()) return;

    const userMessage = messageInput.trim();
    setMessageInput('');
    setIsTyping(true);

    // Create user node
    const userNode: TimelineNode = {
      id: Date.now().toString() + '-user',
      message: userMessage,
      sender: 'user',
      parent: currentNode,
      children: []
    };
    currentNode.children.push(userNode);

    try {
      // Generate AI response with streaming
      const aiResponse = await generateAIResponseWithStreaming(userMessage);

      // Generate choices for the response
      const choices = await generateChoicesForResponse(aiResponse, userMessage);

      // Create AI response node with new options
      const newChoices = choices.map((option: string, index: number) => ({
        id: `response-${Date.now()}-${index}`,
        text: option,
        preview: option.toLowerCase()
      }));

      const aiNode: TimelineNode = {
        id: Date.now().toString() + '-ai',
        message: aiResponse,
        sender: 'ai',
        choices: newChoices,
        parent: userNode,
        children: []
      };
      userNode.children.push(aiNode);

      // Save to database
      if (parameters) {
        const settingId = parameters.settingType === 'character'
          ? parameters.characterSetting?.id
          : parameters.worldSetting?.id;

        if (settingId) {
          const chatContent = {
            content: aiResponse,
            options: choices,
            userChoice: userMessage // Store the user's custom message
          };

          try {
            await window.electronAPI.saveChat(
              null, // parent ID - could be enhanced for branching
              settingId,
              parameters.settingType,
              JSON.stringify(chatContent)
            );
          } catch (error) {
            console.error('Failed to save chat interaction:', error);
          }
        }
      }

      setCurrentNode(aiNode);
      setAdditionalOptions([]); // Reset additional options

    } catch (error) {
      console.error('Failed to process message:', error);
      // Remove the user node if AI response failed
      currentNode.children.pop();
    } finally {
      setIsTyping(false);
      forceUpdate((n) => n + 1);
    }
  };

  const updateCharacterProgress = (choiceId: string) => {
    setCharacter(prev => ({
      ...prev,
      experience: prev.experience + 10,
      stats: {
        ...prev.stats,
        // Add small stat improvements based on choices
        strength: choiceId === 'advance' ? prev.stats.strength + 1 : prev.stats.strength,
        charm: choiceId === 'approach' ? prev.stats.charm + 1 : prev.stats.charm,
        arcana: choiceId === 'investigate' ? prev.stats.arcana + 1 : prev.stats.arcana,
        luck: choiceId === 'signal' ? prev.stats.luck + 1 : prev.stats.luck
      }
    }));
  };

  const handleSaveGame = () => {
    const saveData = {
      character,
      messages,
      gameState,
      timestamp: Date.now()
    };
    localStorage.setItem('rpgChatSave', JSON.stringify(saveData));
    console.log('Game saved!');
  };

  const handleLoadGame = () => {
    const savedData = localStorage.getItem('rpgChatSave');
    if (savedData) {
      const parsed = JSON.parse(savedData);
      setCharacter(parsed.character);
      setMessages(parsed.messages);
      setGameState(parsed.gameState);
      console.log('Game loaded!');
    }
  };



  useEffect(() => {
    if (chatAreaRef.current) {
      chatAreaRef.current.scrollTop = chatAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Add logging for component mount and state
  useEffect(() => {
    console.log('🚀 RPGChat component mounted');
    console.log('📊 Current state:', {
      character,
      gameState,
      timelineRoot,
      currentNode,
      parameters
    });
  }, []);

  // Log when currentNode changes
  useEffect(() => {
    console.log('🔄 Current node changed to:', currentNode);
  }, [currentNode]);

  // Handle timeline navigation (jump to any node)
  const handleJumpToNode = (node: TimelineNode) => {
    setCurrentNode(node);
  };

  // Timeline path for breadcrumb
  const timelinePath = getTimelinePathToNode(currentNode);

  // After render, measure node positions and set SVG lines
  useLayoutEffect(() => {
    if (!timelineOpen || !timelineContainerRef.current) return;
    const containerRect = timelineContainerRef.current.getBoundingClientRect();
    const positions: { [key: string]: { x: number; y: number; width: number; height: number; parentId: string | null } } = {};
    collectNodePositions(timelineRoot, positions, null, containerRect);
    const lines: { x1: number; y1: number; x2: number; y2: number }[] = [];
    Object.values(positions).forEach(pos => {
      if (pos.parentId && positions[pos.parentId]) {
        const parent = positions[pos.parentId];
        lines.push({
          x1: parent.x + parent.width / 2,
          y1: parent.y,
          x2: pos.x - pos.width / 2,
          y2: pos.y,
        });
      }
    });
    setSvgLines(lines);
  }, [timelineRoot, currentNode, timelineOpen]);

  return (
    <div className="min-h-screen bg-gradient-background flex overflow-x-hidden">
      <div className="flex-1 flex flex-col overflow-x-hidden">
        {/* Top Bar */}
        <div className="h-16 bg-card border-b border-border flex items-center justify-between px-4 shadow-accent">
          <div className="flex items-center gap-3">
            {/* Back to Directory Button */}
            <Button
              variant="secondary"
              size="sm"
              className="mr-2"
              onClick={onBackToDirectory || (() => window.location.reload())}
              title="Back to Main Screen"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
            <div>
              <h2 className="font-bold text-foreground">
                {parameters?.settingType === 'character' && parameters.characterSetting 
                  ? parameters.characterSetting.name 
                  : parameters?.settingType === 'world' && parameters.worldSetting 
                    ? parameters.worldSetting.name 
                    : character.name}
              </h2>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setTimelineOpen((open) => !open)}
              title={timelineOpen ? 'Collapse timeline' : 'Expand timeline'}
            >
              {timelineOpen ? 'Hide Timeline' : 'Show Timeline'}
            </Button>
            <Button 
              variant="outline" 
              size="sm"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Collapsible Timeline Breadcrumb */}
        {timelineOpen && (
          <div className="w-full overflow-x-auto overflow-y-visible px-0 py-2 bg-background/80 border-b border-border" style={{ maxHeight: 420, minHeight: 80, position: 'relative' }} ref={timelineContainerRef}>
            {/* SVG lines overlay */}
            <svg style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none', zIndex: 1 }}>
              {svgLines.map((line, i) => (
                <line key={i} x1={line.x1} y1={line.y1} x2={line.x2} y2={line.y2} stroke="#888" strokeWidth={1.5} />
              ))}
            </svg>
            <div className="w-max" style={{ minHeight: 60, display: 'flex', justifyContent: 'center', alignItems: 'center', position: 'relative', zIndex: 2 }}>
              <FloatingTimelineTree node={timelineRoot} currentNode={currentNode} onJump={handleJumpToNode} nodeRefs={nodeRefs} />
            </div>
          </div>
        )}

        {/* Scene Background & Main Text */}
        <div className="flex-1 relative overflow-hidden flex flex-col items-center justify-center">
          <SceneBackground scene={gameState.sceneBackground} mood={gameState.mood} />
          <div className="relative z-10 flex flex-col items-center justify-center h-full">
            {/* Main text */}
            <div id="main-chat-container" className="flex flex-col items-center mb-8 px-4">
              <div className="bg-background/80 rounded-xl p-6 shadow-xl">
                <ReactMarkdown 
                  remarkPlugins={[remarkGfm]}
                  components={{
                    // Custom styling for markdown elements
                    h1: ({children}) => <h1 className="text-2xl font-bold mb-4 text-foreground">{children}</h1>,
                    h2: ({children}) => <h2 className="text-xl font-bold mb-3 text-foreground">{children}</h2>,
                    h3: ({children}) => <h3 className="text-lg font-bold mb-2 text-foreground">{children}</h3>,
                    p: ({children}) => <p className="mb-3 leading-relaxed">{children}</p>,
                    strong: ({children}) => <strong className="font-bold text-foreground">{children}</strong>,
                    em: ({children}) => <em className="italic">{children}</em>,
                    code: ({children}) => <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                    pre: ({children}) => <pre className="bg-muted p-3 rounded-lg overflow-x-auto mb-3">{children}</pre>,
                    blockquote: ({children}) => <blockquote className="border-l-4 border-accent pl-4 italic text-muted-foreground mb-3">{children}</blockquote>,
                    ul: ({children}) => <ul className="list-disc list-inside mb-3 space-y-1">{children}</ul>,
                    ol: ({children}) => <ol className="list-decimal list-inside mb-3 space-y-1">{children}</ol>,
                    li: ({children}) => <li className="leading-relaxed">{children}</li>,
                    table: ({children}) => <table className="w-full border-collapse border border-border mb-3">{children}</table>,
                    th: ({children}) => <th className="border border-border px-3 py-2 bg-muted font-bold">{children}</th>,
                    td: ({children}) => <td className="border border-border px-3 py-2">{children}</td>,
                  }}
                >
                  {currentNode.message}
                </ReactMarkdown>
              </div>
            </div>
            {/* Choices and input (only if AI node) */}
            {currentNode.sender === 'ai' && (
              <div className="w-full max-w-2xl space-y-4">
                {/* Show streaming content if streaming */}
                {isStreamingResponse && streamingContent && (
                  <div className="bg-background/90 rounded-xl p-4 border border-border">
                    <div className="prose prose-invert max-w-none">
                      <ReactMarkdown 
                        remarkPlugins={[remarkGfm]}
                        components={{
                          // Custom styling for markdown elements
                          h1: ({children}) => <h1 className="text-xl font-bold mb-3 text-foreground">{children}</h1>,
                          h2: ({children}) => <h2 className="text-lg font-bold mb-2 text-foreground">{children}</h2>,
                          h3: ({children}) => <h3 className="text-base font-bold mb-2 text-foreground">{children}</h3>,
                          p: ({children}) => <p className="mb-2 leading-relaxed">{children}</p>,
                          strong: ({children}) => <strong className="font-bold text-foreground">{children}</strong>,
                          em: ({children}) => <em className="italic">{children}</em>,
                          code: ({children}) => <code className="bg-muted px-1 py-0.5 rounded text-sm font-mono">{children}</code>,
                          pre: ({children}) => <pre className="bg-muted p-2 rounded-lg overflow-x-auto mb-2 text-sm">{children}</pre>,
                          blockquote: ({children}) => <blockquote className="border-l-4 border-accent pl-3 italic text-muted-foreground mb-2">{children}</blockquote>,
                          ul: ({children}) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                          ol: ({children}) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                          li: ({children}) => <li className="leading-relaxed">{children}</li>,
                          table: ({children}) => <table className="w-full border-collapse border border-border mb-2 text-sm">{children}</table>,
                          th: ({children}) => <th className="border border-border px-2 py-1 bg-muted font-bold">{children}</th>,
                          td: ({children}) => <td className="border border-border px-2 py-1">{children}</td>,
                        }}
                      >
                        {streamingContent}
                      </ReactMarkdown>
                    </div>
                    <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
                      <div className="animate-pulse">●</div>
                      <span>AI is responding...</span>
                    </div>
                  </div>
                )}

                {/* Choice buttons */}
                <ChoiceButtons
                  choices={[...(currentNode.choices || []), ...additionalOptions]}
                  character={character}
                  onChoiceSelect={handleChoiceSelect}
                  disabled={isTyping || isStreamingResponse}
                />

                {/* Enhanced Generate More Options Button */}
                {!isStreamingResponse && (
                  <div className="flex justify-center pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleGenerateMoreOptions}
                      disabled={isGeneratingOptions || isTyping}
                      className="text-sm px-6 py-2 rounded-lg border-2 hover:border-accent hover:bg-accent/5 transition-all duration-200"
                    >
                      {isGeneratingOptions ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                          <span>Generating new options...</span>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <span className="text-lg">🎲</span>
                          <span>Generate More Options</span>
                        </div>
                      )}
                    </Button>
                  </div>
                )}

                {/* Enhanced Custom Message Input */}
                <div className="space-y-4 border-t border-border pt-6">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <span className="font-medium">💬 Write your own response</span>
                  </div>
                  <div className="flex gap-3">
                    <div className="flex-1 relative">
                      <textarea
                        value={messageInput}
                        onChange={(e) => setMessageInput(e.target.value)}
                        placeholder="Share your thoughts, ask questions, or take action..."
                        className="w-full min-h-[100px] p-4 rounded-xl border border-border bg-background/50 text-foreground resize-none focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200 placeholder:text-muted-foreground/60"
                        disabled={isTyping || isStreamingResponse}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSubmitMessage();
                          }
                        }}
                      />
                      <div className="absolute bottom-2 right-2 text-xs text-muted-foreground">
                        {messageInput.length}/500
                      </div>
                    </div>
                    <div className="flex flex-col gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleEnhancePrompt}
                        disabled={!messageInput.trim() || isEnhancing || isTyping || isStreamingResponse}
                        title="Enhance your message with AI suggestions"
                        className="h-12 w-12 p-0 hover:bg-accent/10 hover:border-accent transition-all duration-200"
                      >
                        {isEnhancing ? (
                          <div className="animate-spin h-5 w-5 border-2 border-current border-t-transparent rounded-full" />
                        ) : (
                          <span className="text-lg">✨</span>
                        )}
                      </Button>
                      <Button
                        onClick={handleSubmitMessage}
                        disabled={!messageInput.trim() || isTyping || isStreamingResponse}
                        size="sm"
                        className="h-12 px-4 bg-gradient-to-r from-accent to-accent/80 hover:from-accent/90 hover:to-accent/70 transition-all duration-200 shadow-lg hover:shadow-xl"
                      >
                        {isTyping ? (
                          <div className="flex items-center gap-2">
                            <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                            <span>Sending...</span>
                          </div>
                        ) : (
                          <span className="font-medium">Send Message</span>
                        )}
                      </Button>
                    </div>
                  </div>
                  {messageInput.trim() && (
                    <div className="text-xs text-muted-foreground bg-muted/30 rounded-lg p-2">
                      💡 Tip: Press Enter to send, Shift+Enter for new line
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Persona Images in Bottom Corners */}
      {parameters?.userPersona && (
        <PersonaImage
          image={parameters.userPersona.image}
          name={parameters.userPersona.name}
          position="bottom-left"
        />
      )}
      
      {/* AI Character Image */}
      {parameters?.settingType === 'character' && parameters.characterSetting && (
        <PersonaImage
          image={parameters.characterSetting.imageUrl}
          name={parameters.characterSetting.name}
          position="bottom-right"
        />
      )}
    </div>
  );
};

export default RPGChat;