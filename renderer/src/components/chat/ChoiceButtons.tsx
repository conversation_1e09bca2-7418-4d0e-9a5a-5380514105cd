import React from 'react';
import { DialogueChoice, Character } from '../RPGChat';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { cn } from '../../lib/utils';
import { <PERSON>, Sparkles, AlertTriangle } from 'lucide-react';

interface ChoiceButtonsProps {
  choices: DialogueChoice[];
  character: Character;
  onChoiceSelect: (choice: DialogueChoice) => void;
  disabled?: boolean;
}

export const ChoiceButtons: React.FC<ChoiceButtonsProps> = ({
  choices,
  character,
  onChoiceSelect,
  disabled
}) => {
  const isChoiceAvailable = (choice: DialogueChoice): boolean => {
    if (!choice.requirements) return true;
    
    const { stat, minValue, hasItem } = choice.requirements;
    
    if (stat && minValue) {
      return character.stats[stat] >= minValue;
    }
    
    if (hasItem) {
      return character.inventory.some(item => item.name === hasItem);
    }
    
    return true;
  };

  const getRequirementText = (choice: DialogueChoice): string => {
    if (!choice.requirements) return '';
    
    const { stat, minValue, hasItem } = choice.requirements;
    
    if (stat && minValue) {
      const statName = stat.charAt(0).toUpperCase() + stat.slice(1);
      return `Requires ${statName} ${minValue}+`;
    }
    
    if (hasItem) {
      return `Requires ${hasItem}`;
    }
    
    return '';
  };

  const getStatIcon = (stat: string) => {
    const icons = {
      strength: '💪',
      charm: '💫',
      arcana: '🔮',
      luck: '🍀'
    };
    return icons[stat as keyof typeof icons] || '⭐';
  };

  if (choices.length === 0) return null;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Sparkles className="h-4 w-4" />
        <span className="font-medium">🎯 Choose your response</span>
      </div>
      {/* Flexible grid for options */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {choices.map((choice) => {
          const isAvailable = isChoiceAvailable(choice);
          const requirementText = getRequirementText(choice);

          return (
            <Button
              key={choice.id}
              variant={isAvailable ? "outline" : "secondary"}
              disabled={disabled || !isAvailable}
              onClick={() => onChoiceSelect(choice)}
              className={cn(
                "h-auto min-h-[120px] p-5 text-left justify-start transition-all duration-300 rounded-xl",
                "hover:shadow-lg hover:scale-[1.02] border-2 w-full",
                isAvailable 
                  ? "border-border hover:border-accent hover:bg-accent/5 hover:shadow-accent/20" 
                  : "opacity-60 cursor-not-allowed border-dashed",
                choice.consequence && "hover:animate-pulse hover:shadow-orange-500/20"
              )}
            >
              <div className="flex-1 space-y-3 w-full">
                <div className="flex items-start justify-between gap-3 w-full">
                  <span className="font-semibold text-base leading-tight text-foreground break-words flex-1">
                    {choice.text}
                  </span>
                  {!isAvailable && (
                    <Lock className="h-5 w-5 text-muted-foreground flex-shrink-0 ml-2" />
                  )}
                </div>
                {choice.preview && (
                  <p className="text-sm text-muted-foreground/80 italic leading-relaxed break-words">
                    {choice.preview}
                  </p>
                )}
                <div className="flex items-start gap-2 flex-wrap">
                  {requirementText && (
                    <Badge 
                      variant={isAvailable ? "secondary" : "destructive"}
                      className={cn(
                        "text-xs font-medium px-2 py-1 break-words",
                        isAvailable ? "bg-muted/50 text-muted-foreground" : "bg-destructive/20 text-destructive"
                      )}
                    >
                      {choice.requirements?.stat && getStatIcon(choice.requirements.stat)}
                      {requirementText}
                    </Badge>
                  )}
                  {choice.consequence && (
                    <Badge 
                      variant="outline" 
                      className="text-xs font-medium px-2 py-1 bg-orange-500/10 text-orange-600 border-orange-500/20 break-words"
                    >
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      {choice.consequence}
                    </Badge>
                  )}
                </div>
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );
};