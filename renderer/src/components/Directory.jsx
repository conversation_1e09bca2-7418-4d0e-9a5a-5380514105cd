import React, { useState, useEffect } from 'react';
import RPGChat from './RPGChat';
import ChatSetup from './ChatSetup';
import ApiKeySetup from './ApiKeySetup';
import SystemPromptSettings from './SystemPromptSettings';
import PersonaCreation from './CharacterCreation';
import PersonaManagement from './PersonaManagement';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Settings, Plus, Zap, UserPlus, MessageSquare, Users, Globe, Clock, Play, Edit } from 'lucide-react';

const Directory = () => {
  const [characters, setCharacters] = useState([]);
  const [worlds, setWorlds] = useState([]);
  const [recentChats, setRecentChats] = useState([]);
  const [stage, setStage] = useState('directory'); // 'directory', 'apiKey', 'systemPrompts', 'setup', 'character', 'personaManagement', 'chat'
  const [currentChat, setCurrentChat] = useState(null);
  const [chatSetupKey, setChatSetupKey] = useState(0); // Key to force ChatSetup re-render
  const [loading, setLoading] = useState(true);
  const [editingCharacter, setEditingCharacter] = useState(null); // For CharacterCreation

  // Load characters, worlds, and recent chats
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Load characters
        const charactersResult = await window.electronAPI.getCharacterSettings();
        if (charactersResult.success) {
          setCharacters(charactersResult.settings);
        }
        
        // Load worlds
        const worldsResult = await window.electronAPI.getWorldSettings();
        if (worldsResult.success) {
          setWorlds(worldsResult.settings);
        }
        
        // Load recent chats
        const recentChatsResult = await window.electronAPI.getRecentChats(20);
        if (recentChatsResult.success) {
          setRecentChats(recentChatsResult.chats);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const reset = () => {
    setStage('directory');
    setEditingCharacter(null);
  };

  // Helper function to reconstruct timeline from chat database entries
  const reconstructTimelineFromChats = (chats) => {
    console.log('🔧 Reconstructing timeline from', chats.length, 'chat entries');
    
    if (chats.length === 0) return null;
    
    // Sort chats by creation time to ensure proper order
    const sortedChats = chats.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
    
    // Create a map to track nodes by their IDs
    const nodeMap = new Map();
    let rootNode = null;
    
    // Process each chat entry
    sortedChats.forEach((chat, index) => {
      try {
        const chatData = JSON.parse(chat.contents);
        console.log(`📄 Processing chat ${index + 1}:`, chatData);
        
        // Create AI response node
        const aiNode = {
          id: `ai-${chat.id}`,
          message: chatData.content || 'AI response',
          sender: 'ai',
          choices: chatData.options?.map((option, optIndex) => ({
            id: `choice-${chat.id}-${optIndex}`,
            text: option,
            preview: option.toLowerCase()
          })) || [],
          children: [],
          parent: null
        };
        
        nodeMap.set(aiNode.id, aiNode);
        
        // If this is the first chat, it's the root
        if (index === 0) {
          rootNode = aiNode;
        } else {
          // Find the parent node (previous AI response)
          const parentChat = sortedChats[index - 1];
          const parentNode = nodeMap.get(`ai-${parentChat.id}`);
          
          if (parentNode) {
            // Create user node for the choice that led to this response
            const userChoice = chatData.userChoice || 'User choice';
            const userNode = {
              id: `user-${chat.id}`,
              message: userChoice,
              sender: 'user',
              parent: parentNode,
              children: [aiNode],
              choiceId: `choice-${parentChat.id}-0` // We could enhance this to find the actual choice ID
            };
            
            aiNode.parent = userNode;
            parentNode.children.push(userNode);
            nodeMap.set(userNode.id, userNode);
          }
        }
      } catch (error) {
        console.error('❌ Failed to parse chat entry:', error, chat);
      }
    });
    
    console.log('✅ Reconstructed timeline root:', rootNode);
    return rootNode;
  };

  const newChatFlow = async () => {
    const { success, apiKey } = await window.electronAPI.getActiveApiKey().catch(() => ({}));
    setStage(success && apiKey ? 'setup' : 'apiKey');
  };

  const openCharacterChat = async (character) => {
    console.log('🔍 openCharacterChat called with character:', character);
    try {
      // Get all chats for this character to reconstruct the full timeline
      console.log('📡 Fetching all chats for character ID:', character.id);
      const chatsResult = await window.electronAPI.getChats(character.id, 'character');
      console.log('📡 getChats result:', chatsResult);
      
      let chatData = null;
      let fullTimeline = null;
      
      if (chatsResult.success && chatsResult.chats.length > 0) {
        console.log('📄 Found', chatsResult.chats.length, 'chat entries');
        
        // Reconstruct the full conversation timeline
        fullTimeline = reconstructTimelineFromChats(chatsResult.chats);
        console.log('🔄 Reconstructed timeline:', fullTimeline);
        
        // Use the latest chat data for current state
        const latestChat = chatsResult.chats[chatsResult.chats.length - 1];
        console.log('📄 Latest chat data:', latestChat);
        
        try {
          chatData = JSON.parse(latestChat.contents);
          console.log('✅ Successfully parsed chat data:', chatData);
        } catch (parseError) {
          console.error('❌ Failed to parse chat contents:', parseError);
          console.log('📄 Raw chat contents:', latestChat.contents);
        }
      } else {
        console.log('ℹ️ No existing chats found for character');
      }
      
      // Set up the chat with character data
      const chatParams = {
        settingType: 'character',
        characterSetting: character,
        existingChat: chatData,
        fullTimeline: fullTimeline
      };
      
      console.log('🎯 Final chat params:', chatParams);
      setCurrentChat({ isNew: !chatData, params: chatParams });
      setStage('chat');
    } catch (error) {
      console.error('❌ Error opening character chat:', error);
      // Fallback to new chat
      const chatParams = {
        settingType: 'character',
        characterSetting: character
      };
      console.log('🔄 Falling back to new chat with params:', chatParams);
      setCurrentChat({ isNew: true, params: chatParams });
      setStage('chat');
    }
  };

  const startNewChatWithCharacter = (character) => {
    // Convert character setting to persona format for editing
    const characterAsPersona = {
      id: character.id,
      name: character.name,
      personality: character.personality,
      appearance: character.appearance,
      image: character.imageUrl,
      created_at: character.created_at
    };
    
    setEditingCharacter(characterAsPersona);
    setStage('setup');
  };

  const handleCharacterCreationComplete = () => {
    setEditingCharacter(null);
    // Reload characters to get updated data
    const loadData = async () => {
      try {
        const charactersResult = await window.electronAPI.getCharacterSettings();
        if (charactersResult.success) {
          setCharacters(charactersResult.settings);
        }
      } catch (error) {
        console.error('Error reloading characters:', error);
      }
    };
    loadData();
    setStage('directory');
  };

  const openWorldChat = async (world) => {
    console.log('🌍 openWorldChat called with world:', world);
    try {
      // Get all chats for this world to reconstruct the full timeline
      console.log('📡 Fetching all chats for world ID:', world.id);
      const chatsResult = await window.electronAPI.getChats(world.id, 'world');
      console.log('📡 getChats result for world:', chatsResult);

      let chatData = null;
      let fullTimeline = null;

      if (chatsResult.success && chatsResult.chats.length > 0) {
        console.log('📄 Found', chatsResult.chats.length, 'world chat entries');
        
        // Reconstruct the full conversation timeline
        fullTimeline = reconstructTimelineFromChats(chatsResult.chats);
        console.log('🔄 Reconstructed world timeline:', fullTimeline);
        
        // Use the latest chat data for current state
        const latestChat = chatsResult.chats[chatsResult.chats.length - 1];
        console.log('📄 Latest world chat data:', latestChat);
        
        try {
          chatData = JSON.parse(latestChat.contents);
          console.log('✅ Successfully parsed world chat data:', chatData);
        } catch (parseError) {
          console.error('❌ Failed to parse world chat contents:', parseError);
          console.log('📄 Raw world chat contents:', latestChat.contents);
        }
      } else {
        console.log('ℹ️ No existing chats found for world');
      }

      // Set up the chat with world data
      const chatParams = {
        settingType: 'world',
        worldSetting: world,
        existingChat: chatData,
        fullTimeline: fullTimeline
      };

      console.log('🎯 Final world chat params:', chatParams);
      setCurrentChat({ isNew: !chatData, params: chatParams });
      setStage('chat');
    } catch (error) {
      console.error('❌ Error opening world chat:', error);
      // Fallback to new chat
      const chatParams = {
        settingType: 'world',
        worldSetting: world
      };
      console.log('🔄 Falling back to new world chat with params:', chatParams);
      setCurrentChat({ isNew: true, params: chatParams });
      setStage('chat');
    }
  };

  const openSpecificChat = async (chat) => {
    console.log('🎯 openSpecificChat called with chat:', chat);
    try {
      // Parse the specific chat data
      let chatData = null;
      try {
        console.log('📄 Attempting to parse chat contents:', chat.contents);
        chatData = JSON.parse(chat.contents);
        console.log('✅ Successfully parsed specific chat data:', chatData);
      } catch (parseError) {
        console.error('❌ Failed to parse specific chat contents:', parseError);
        console.log('📄 Raw specific chat contents:', chat.contents);
        // If not JSON, treat as plain text (backward compatibility)
        chatData = { content: chat.contents, options: [] };
        console.log('🔄 Using fallback chat data:', chatData);
      }

      if (chat.setting_type === 'character') {
        console.log('👤 Looking for character with ID:', chat.setting_id);
        const character = characters.find(c => c.id === chat.setting_id);
        console.log('👤 Found character:', character);
        if (character) {
          const chatParams = {
            settingType: 'character',
            characterSetting: character,
            existingChat: chatData
          };
          console.log('🎯 Setting up character chat with params:', chatParams);
          setCurrentChat({ isNew: false, params: chatParams });
          setStage('chat');
        } else {
          console.error('❌ Character not found for ID:', chat.setting_id);
        }
      } else if (chat.setting_type === 'world') {
        console.log('🌍 Looking for world with ID:', chat.setting_id);
        const world = worlds.find(w => w.id === chat.setting_id);
        console.log('🌍 Found world:', world);
        if (world) {
          const chatParams = {
            settingType: 'world',
            worldSetting: world,
            existingChat: chatData
          };
          console.log('🎯 Setting up world chat with params:', chatParams);
          setCurrentChat({ isNew: false, params: chatParams });
          setStage('chat');
        } else {
          console.error('❌ World not found for ID:', chat.setting_id);
        }
      }
    } catch (error) {
      console.error('❌ Error opening specific chat:', error);
      // Fallback to opening the character/world chat normally
      if (chat.setting_type === 'character') {
        console.log('🔄 Falling back to character chat');
        const character = characters.find(c => c.id === chat.setting_id);
        if (character) openCharacterChat(character);
      } else if (chat.setting_type === 'world') {
        console.log('🔄 Falling back to world chat');
        const world = worlds.find(w => w.id === chat.setting_id);
        if (world) openWorldChat(world);
      }
    }
  };

  // Render based on current stage
  if (stage === 'apiKey') return <ApiKeySetup onApiKeySet={() => setStage('setup')} onBack={reset} onShowSystemPrompts={() => setStage('systemPrompts')} />;
  if (stage === 'systemPrompts') return <SystemPromptSettings onBack={() => setStage('apiKey')} />;
  if (stage === 'character') return <PersonaCreation onBack={reset} onPersonaCreated={handleCharacterCreationComplete} editingPersona={editingCharacter} />;
  if (stage === 'personaManagement') return <PersonaManagement onBack={reset} />;
  if (stage === 'setup') return (
    <ChatSetup
      key={chatSetupKey}
      onStartChat={(params) => { setCurrentChat({ isNew: true, params }); setStage('chat'); }}
      onBack={reset}
      onNavigateToCharacterCreation={() => setStage('character')}
      editingCharacter={editingCharacter}
    />
  );
  if (stage === 'chat') return (
    <RPGChat
      parameters={currentChat.params}
      onBackToDirectory={reset}
    />
  );

  // Directory view
  return (
    <div className="min-h-screen bg-black text-white p-6">
      <header className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <img 
            src="/assets/Icon.png" 
            alt="Electrobox RPG Logo" 
            className="w-10 h-10 rounded-lg"
          />
          <h1 className="text-2xl font-bold text-blue-400">Electrobox RPG</h1>
        </div>
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setStage('personaManagement')}
            className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white"
          >
            <UserPlus className="w-4 h-4 mr-2" /> Edit Personas
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setStage('apiKey')}
            className="border-blue-500 text-blue-400 hover:bg-blue-500 hover:text-white"
          >
            <Settings className="w-4 h-4 mr-2" /> API Key
          </Button>
        </div>
      </header>

      <main className="max-w-6xl mx-auto">
        {/* New Chat Section */}
        <div className="mb-8 text-center">
          <Button 
            size="lg" 
            onClick={newChatFlow}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg font-semibold"
          >
            <Plus className="w-5 h-5 mr-2" /> Start New Adventure
          </Button>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto"></div>
            <p className="text-gray-400 mt-4">Loading your adventures...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Characters Section */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <h2 className="text-xl font-semibold text-blue-400 mb-4 flex items-center gap-2">
                <Users className="w-5 h-5" /> Your Characters
              </h2>
              
              {characters.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {characters.map((character) => (
                    <Card 
                      key={character.id} 
                      className="hover:shadow-lg transition-all duration-200 bg-gray-800 border-gray-700"
                    >
                      <CardHeader className="pb-3">
                        <div className="flex flex-col items-center text-center space-y-3">
                          {character.imageUrl ? (
                            <img 
                              src={character.imageUrl} 
                              alt={character.name}
                              className="w-20 h-20 rounded-full object-cover border-2 border-gray-200 shadow-md"
                            />
                          ) : (
                            <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border-2 border-blue-300 shadow-md">
                              <Users className="w-10 h-10 text-blue-600" />
                            </div>
                          )}
                          <div>
                            <CardTitle className="text-lg font-bold text-white">{character.name}</CardTitle>
                            <CardDescription className="text-xs text-gray-400">
                              Created {new Date(character.created_at).toLocaleDateString()}
                            </CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2">
                          <div>
                            <h4 className="text-sm font-semibold text-gray-300 mb-1">Personality</h4>
                            <p className="text-sm text-gray-400 line-clamp-3 leading-relaxed">
                              {character.personality}
                            </p>
                          </div>
                          {character.appearance && (
                            <div>
                              <h4 className="text-sm font-semibold text-gray-300 mb-1">Appearance</h4>
                              <p className="text-sm text-gray-400 line-clamp-2 leading-relaxed">
                                {character.appearance}
                              </p>
                            </div>
                          )}
                          <div className="pt-2 space-y-2">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => openCharacterChat(character)}
                              className="w-full border-blue-300 text-blue-400 hover:bg-blue-500 hover:text-white"
                            >
                              <MessageSquare className="w-4 h-4 mr-2" />
                              Continue Chat
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => startNewChatWithCharacter(character)}
                              className="w-full border-green-300 text-green-400 hover:bg-green-500 hover:text-white"
                            >
                              <Play className="w-4 h-4 mr-2" />
                              Start New Chat
                            </Button>

                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400 text-lg">No characters created yet.</p>
                  <p className="text-gray-500 text-sm mt-2">Create your first character to begin!</p>
                </div>
              )}
            </div>

            {/* Worlds Section */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <h2 className="text-xl font-semibold text-blue-400 mb-4 flex items-center gap-2">
                <Globe className="w-5 h-5" /> Your Worlds
              </h2>
              
              {worlds.length > 0 ? (
                <div className="space-y-3">
                  {worlds.map((world) => (
                    <div
                      key={world.id}
                      className="p-4 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 hover:border-blue-500 cursor-pointer transition-all duration-200"
                      onClick={() => openWorldChat(world)}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-white mb-1">{world.name}</h3>
                          <p className="text-sm text-gray-400 line-clamp-2 mb-2">
                            {world.world_description}
                          </p>
                          <p className="text-xs text-gray-500">
                            Created: {new Date(world.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Button 
                          size="sm" 
                          className="bg-blue-600 hover:bg-blue-700 text-white ml-3"
                        >
                          Chat
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-400 text-lg">No worlds created yet.</p>
                  <p className="text-gray-500 text-sm mt-2">Create your first world to begin!</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Recent Chats Section */}
        {recentChats.length > 0 && (
          <div className="mt-8 bg-gray-900 rounded-lg p-6 border border-gray-800">
            <h2 className="text-xl font-semibold text-blue-400 mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" /> Recent Adventures
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {recentChats.slice(0, 6).map((chat, index) => (
                <div
                  key={`${chat.setting_id}-${chat.setting_type}-${index}`}
                  className="p-3 bg-gray-800 rounded-lg border border-gray-700 hover:bg-gray-700 hover:border-blue-500 cursor-pointer transition-all duration-200"
                  onClick={() => openSpecificChat(chat)}
                >
                  <div className="flex items-center gap-2">
                    {chat.setting_type === 'character' ? (
                      <Users className="w-4 h-4 text-blue-400" />
                    ) : (
                      <Globe className="w-4 h-4 text-green-400" />
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium text-white text-sm truncate">
                        {chat.setting_name || 'Unknown'}
                      </h3>
                      <p className="text-xs text-gray-400">
                        {new Date(chat.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default Directory;
