const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Database operations
  saveMessage: (message) => ipcRenderer.invoke('save-message', message),
  getMessages: () => ipcRenderer.invoke('get-messages'),
  clearMessages: () => ipcRenderer.invoke('clear-messages'),
  
  // AI API operations
  sendToAI: (message) => ipcRenderer.invoke('send-to-ai', message),
  
  // Settings operations
  saveApiKey: (apiKey, name, setActive) => ipcRenderer.invoke('save-api-key', apiKey, name, setActive),
  getApiKeys: () => ipcRenderer.invoke('get-api-keys'),
  getActiveApiKey: () => ipcRenderer.invoke('get-active-api-key'),
  setActiveApiKey: (id) => ipc<PERSON>enderer.invoke('set-active-api-key', id),
  deleteApiKey: (id) => ipcRenderer.invoke('delete-api-key', id),
  updateApiKeyName: (id, name) => ipcRenderer.invoke('update-api-key-name', id, name),
  getApiKey: () => ipcRenderer.invoke('get-api-key'),
  testApiKey: (apiKey) => ipcRenderer.invoke('test-api-key', apiKey),
  
  // System prompt settings
  getSystemPromptSettings: () => ipcRenderer.invoke('get-system-prompt-settings'),
  getDefaultSystemPrompt: () => ipcRenderer.invoke('get-default-system-prompt'),
  setDefaultSystemPrompt: (id) => ipcRenderer.invoke('set-default-system-prompt', id),
  saveSystemPromptSetting: (name, description, prompt, isDefault) => ipcRenderer.invoke('save-system-prompt-setting', name, description, prompt, isDefault),
  updateSystemPromptSetting: (id, name, description, prompt) => ipcRenderer.invoke('update-system-prompt-setting', id, name, description, prompt),
  deleteSystemPromptSetting: (id) => ipcRenderer.invoke('delete-system-prompt-setting', id),
  
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),

  // Character settings
  saveCharacterSetting: (characterSetting) => ipcRenderer.invoke('save-character-setting', characterSetting),
  getCharacterSettings: () => ipcRenderer.invoke('get-character-settings'),
  getCharacterSetting: (id) => ipcRenderer.invoke('get-character-setting', id),

  // World settings
  saveWorldSetting: (worldSetting) => ipcRenderer.invoke('save-world-setting', worldSetting),
  getWorldSettings: () => ipcRenderer.invoke('get-world-settings'),
  getWorldSetting: (id) => ipcRenderer.invoke('get-world-setting', id),

  // Chats
  saveChat: (parentId, settingId, settingType, contents) => ipcRenderer.invoke('save-chat', parentId, settingId, settingType, contents),
  getChats: (settingId, settingType) => ipcRenderer.invoke('get-chats', settingId, settingType),
  getRecentChats: (limit) => ipcRenderer.invoke('get-recent-chats', limit),
  
  // Persona creation
  savePersona: (persona) => ipcRenderer.invoke('save-persona', persona),
  getPersonas: () => ipcRenderer.invoke('get-personas'),
  getPersona: (id) => ipcRenderer.invoke('get-persona', id),
  deletePersona: (id) => ipcRenderer.invoke('delete-persona', id),
  enhanceCharacterText: (data) => ipcRenderer.invoke('enhance-character-text', data),
  enhanceCharacterTextStream: (data) => ipcRenderer.invoke('enhance-character-text-stream', data),
  onEnhanceCharacterTextChunk: (callback) => ipcRenderer.on('enhance-character-text-chunk', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
});